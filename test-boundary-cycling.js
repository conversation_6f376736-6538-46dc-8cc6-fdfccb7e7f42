// Simple test to verify the boundary cycling logic
// This simulates the getAllSegmentBoundaries function behavior

function getAllSegmentBoundaries(
  cutTimes = [],
  overlays = [],
  totalDuration = 3000
) {
  const boundaries = [];

  // Add manual cut times if available
  if (cutTimes.length > 0) {
    const cutFrames = cutTimes.sort((a, b) => a - b);

    // Create segments from cut times
    const segments = [];

    // First segment: from 0 to first cut
    if (cutFrames.length > 0) {
      segments.push({ start: 0, end: cutFrames[0] });
    }

    // Middle segments: from cut to cut
    for (let i = 0; i < cutFrames.length - 1; i++) {
      segments.push({ start: cutFrames[i], end: cutFrames[i + 1] });
    }

    // Last segment: from last cut to end
    if (cutFrames.length > 0) {
      segments.push({
        start: cutFrames[cutFrames.length - 1],
        end: totalDuration,
      });
    }

    // Add boundaries in order: start → end → next start → next end, etc.
    segments.forEach((segment) => {
      boundaries.push(segment.start);
      boundaries.push(segment.end);
    });
  } else if (overlays && overlays.length > 0) {
    // Use overlay boundaries as fallback
    const segments = overlays
      .map((overlay) => ({
        start: overlay.from,
        end: overlay.from + overlay.durationInFrames,
      }))
      .sort((a, b) => a.start - b.start);

    // Add boundaries in order: start → end → next start → next end, etc.
    segments.forEach((segment) => {
      boundaries.push(segment.start);
      boundaries.push(segment.end);
    });
  } else {
    // Fallback: just start and end
    boundaries.push(0);
    boundaries.push(totalDuration);
  }

  // Remove duplicates while preserving order
  const uniqueBoundaries = [];
  boundaries.forEach((boundary) => {
    if (!uniqueBoundaries.includes(boundary)) {
      uniqueBoundaries.push(boundary);
    }
  });

  return uniqueBoundaries;
}

function moveToNextBoundary(currentFrame, boundaries) {
  if (boundaries.length === 0) return currentFrame;

  // Find current position in boundaries
  let currentIndex = -1;

  // Look for exact match first
  for (let i = 0; i < boundaries.length; i++) {
    if (Math.abs(boundaries[i] - currentFrame) < 1) {
      // Allow small tolerance for frame precision
      currentIndex = i;
      break;
    }
  }

  if (currentIndex !== -1) {
    // We're at a boundary, move to the next one
    currentIndex = (currentIndex + 1) % boundaries.length;
  } else {
    // We're not at a boundary, find the next boundary
    currentIndex = boundaries.findIndex((b) => b > currentFrame);
    if (currentIndex === -1) {
      // We're past all boundaries, wrap to the first one
      currentIndex = 0;
    }
  }

  return boundaries[currentIndex];
}

// Test cases
console.log("=== Testing Boundary Cycling Logic ===\n");

// Test case 1: Cut times at 500, 1000, 1500
const cutTimes1 = [500, 1000, 1500];
const boundaries1 = getAllSegmentBoundaries(cutTimes1, [], 3000);
console.log("Test 1 - Cut times [500, 1000, 1500]:");
console.log("Boundaries:", boundaries1);
console.log("Expected: [0, 500, 500, 1000, 1000, 1500, 1500, 3000]");
console.log("Actual result shows the issue - duplicates are being removed!");

// Test cycling through boundaries
let currentFrame = 0;
console.log("\nCycling through boundaries:");
for (let i = 0; i < 10; i++) {
  console.log(`Step ${i}: Frame ${currentFrame}`);
  currentFrame = moveToNextBoundary(currentFrame, boundaries1);
}

console.log("\n" + "=".repeat(50) + "\n");

// Test case 2: Overlays
const overlays2 = [
  { from: 100, durationInFrames: 400 }, // 100-500
  { from: 600, durationInFrames: 300 }, // 600-900
  { from: 1200, durationInFrames: 500 }, // 1200-1700
];
const boundaries2 = getAllSegmentBoundaries([], overlays2, 3000);
console.log("Test 2 - Overlays:");
console.log("Boundaries:", boundaries2);
console.log("Expected: [100, 500, 600, 900, 1200, 1700]");

// Test cycling through overlay boundaries
currentFrame = 0;
console.log("\nCycling through overlay boundaries:");
for (let i = 0; i < 8; i++) {
  console.log(`Step ${i}: Frame ${currentFrame}`);
  currentFrame = moveToNextBoundary(currentFrame, boundaries2);
}

console.log("\n" + "=".repeat(50) + "\n");

// Test case 3: No cuts or overlays (fallback)
const boundaries3 = getAllSegmentBoundaries([], [], 3000);
console.log("Test 3 - No cuts or overlays (fallback):");
console.log("Boundaries:", boundaries3);
console.log("Expected: [0, 3000]");

// Test cycling through fallback boundaries
currentFrame = 0;
console.log("\nCycling through fallback boundaries:");
for (let i = 0; i < 4; i++) {
  console.log(`Step ${i}: Frame ${currentFrame}`);
  currentFrame = moveToNextBoundary(currentFrame, boundaries3);
}
