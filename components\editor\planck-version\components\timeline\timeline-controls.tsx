import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Play,
  Pause,
  ZoomOut,
  ZoomIn,
  Undo2,
  Redo2,
  SquareSquare,
  ChevronLeft,
  ChevronRight,
  Scissors,
} from "lucide-react";
import { useEditorContext } from "../../contexts/editor-context";
import { useTimeline } from "../../contexts/timeline-context";
import { ZOOM_CONSTRAINTS } from "../../constants";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { Slider } from "@/components/ui/slider";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useTimelineShortcuts } from "../../hooks/use-timeline-shortcuts";
import { useAssetLoading } from "../../contexts/asset-loading-context";
import { useKeyframeContext } from "../../contexts/keyframe-context";
import { Separator } from "@/components/ui/separator";
import { getEffectiveFPS } from "../../utils/fps-utils";
import { CreateSegmentPopup } from "./CreateSegmentPopup";
import { AutoCutPopup } from "./AutoCutPopup";
import { useSegmentStore } from "../../../../../store/segmentStore";
import { useCutStore } from "@/store/cutStore";
type AspectRatioOption = "16:9" | "9:16" | "1:1" | "4:5";
const formatTime = (frames: number, fps: number): string => {
  // Calculate exact time using actual FPS for accuracy
  const exactTimeSeconds = frames / fps;

  // For times very close to round seconds (within 1/60th second), round up to the next second
  const shouldRoundToNextSecond = exactTimeSeconds % 1 >= 0.983; // 59/60 = 0.983...
  const adjustedTimeSeconds = shouldRoundToNextSecond
    ? Math.ceil(exactTimeSeconds)
    : exactTimeSeconds;

  const totalSeconds = Math.floor(adjustedTimeSeconds);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  // Calculate frame within current second
  const frameInSecond = frames - Math.floor(exactTimeSeconds) * fps;
  const displayFrame = shouldRoundToNextSecond ? 0 : Math.floor(frameInSecond);
  // console.log('formatTime debug:', {
  //   frames,
  //   fps,
  //   exactTimeSeconds: exactTimeSeconds.toFixed(3),
  //   shouldRoundToNextSecond,
  //   adjustedTimeSeconds: adjustedTimeSeconds.toFixed(3),
  //   totalSeconds,
  //   frameInSecond: frameInSecond.toFixed(3),
  //   displayFrame
  // });
  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}.${displayFrame
    .toString()
    .padStart(2, "0")}`;
};
interface TimelineControlsProps {
  isPlaying: boolean;
  togglePlayPause: () => void;
  currentFrame: number;
  totalDuration: number;
  formatTime?: (frames: number) => string;
  setCurrentFrame?: (frame: number) => void;
  fps?: number;
  overlays?: any[];
  onSplitAtTime?: (timeInSeconds: number, timeData?: any) => void;
}
export const TimelineControls: React.FC<TimelineControlsProps> = ({
  isPlaying,
  togglePlayPause,
  currentFrame,
  totalDuration,
  formatTime: externalFormatTime,
  setCurrentFrame,
  fps = 30,
  overlays = [],
  onSplitAtTime,
}) => {
  const effectiveFPS = overlays.length > 0 ? getEffectiveFPS(overlays) : fps;
  const [displayFrame, setDisplayFrame] = useState(currentFrame);
  const [isDraggingPlayhead, setIsDraggingPlayhead] = useState(false);
  const { addCutTime, cutTimes: storeCutTimes, clearCutTimes } = useCutStore();
  const setTimeFrameSegment = useSegmentStore(
    (state) => state.setTimeFrameSegment
  );
  useEffect(() => {
    const handleFrameUpdate = (event: CustomEvent) => {
      const { frame, isDragging, videoRestart } = event.detail;
      setDisplayFrame(frame);
      setIsDraggingPlayhead(isDragging);
      // Handle video restart event
      if (videoRestart) {
        setDisplayFrame(0);
      }
    };
    window.addEventListener(
      "timeline-frame-update",
      handleFrameUpdate as EventListener
    );
    return () => {
      window.removeEventListener(
        "timeline-frame-update",
        handleFrameUpdate as EventListener
      );
    };
  }, []);
  useEffect(() => {
    if (!isDraggingPlayhead) {
      setDisplayFrame(currentFrame);
    }
  }, [currentFrame, isDraggingPlayhead]);
  const {
    setAspectRatio,
    deleteOverlaysByRow,
    undo,
    redo,
    canUndo,
    canRedo,
    playbackRate,
    setPlaybackRate,
    durationInFrames,
  } = useEditorContext();
  const { visibleRows, removeRow, zoomScale, setZoomScale } = useTimeline();
  useTimelineShortcuts({
    handlePlayPause: () => {
      togglePlayPause();
    },
    undo,
    redo,
    canUndo,
    canRedo,
    zoomScale,
    setZoomScale,
  });
  const { clearAllKeyframes } = useKeyframeContext();
  // FIXED: Remove the auto-pause logic from TimelineControls
  // Let the useVideoPlayer hook handle end-of-video detection
  // This prevents conflicts and ensures consistent behavior
  const goToPreviousFrame = useCallback(() => {
    if (setCurrentFrame) {
      const newFrame = Math.max(0, currentFrame - 1);
      setCurrentFrame(newFrame);
      // Update display frame immediately for accurate visual feedback
      setDisplayFrame(newFrame);
    }
  }, [currentFrame, setCurrentFrame]);

  const goToNextFrame = useCallback(() => {
    if (setCurrentFrame) {
      // Use totalDuration - 1 as max since frames are 0-indexed
      const newFrame = Math.min(totalDuration - 1, currentFrame + 1);
      setCurrentFrame(newFrame);
      // Update display frame immediately for accurate visual feedback
      setDisplayFrame(newFrame);
    }
  }, [currentFrame, totalDuration, setCurrentFrame]);
  const handlePlayPause = () => {
    togglePlayPause();
  };

  const handleSliderChange = useCallback(
    (value: number[]) => {
      setZoomScale(value[0] / 100);
    },
    [setZoomScale]
  );

  const handleZoomOut = useCallback(() => {
    const newScale = Math.max(
      ZOOM_CONSTRAINTS.min,
      zoomScale - ZOOM_CONSTRAINTS.step
    );
    setZoomScale(newScale);
  }, [zoomScale, setZoomScale]);
  const handleZoomIn = useCallback(() => {
    const newScale = Math.min(
      ZOOM_CONSTRAINTS.max,
      zoomScale + ZOOM_CONSTRAINTS.step
    );
    setZoomScale(newScale);
  }, [zoomScale, setZoomScale]);
  const handleResetZoom = () => {
    setZoomScale(ZOOM_CONSTRAINTS.min);
  };
  // Convert CutTime to frame number
  const cutTimeToFrame = useCallback(
    (cutTime: any) => {
      const totalSeconds =
        cutTime.hours * 3600 + cutTime.minutes * 60 + cutTime.seconds;
      const totalFrames = totalSeconds * effectiveFPS + cutTime.frames;
      return Math.round(totalFrames);
    },
    [effectiveFPS]
  );

  // Get all segment boundaries in proper cycling order (start → end → next start → next end, etc.)
  const getAllSegmentBoundaries = useCallback(() => {
    const boundaries: number[] = [];

    // Add manual cut times if available
    if (storeCutTimes.length > 0) {
      const cutFrames = storeCutTimes
        .map((cutTime) => cutTimeToFrame(cutTime))
        .sort((a, b) => a - b);

      // Create segments from cut times
      const segments: Array<{ start: number; end: number }> = [];

      // First segment: from 0 to first cut (cut frame is the boundary)
      if (cutFrames.length > 0) {
        segments.push({ start: 0, end: cutFrames[0] });
      }

      // Middle segments: from cut to cut (cut frame is the boundary)
      for (let i = 0; i < cutFrames.length - 1; i++) {
        segments.push({ start: cutFrames[i], end: cutFrames[i + 1] });
      }

      // Last segment: from last cut to end - 1
      if (cutFrames.length > 0) {
        segments.push({
          start: cutFrames[cutFrames.length - 1],
          end: totalDuration - 1,
        });
      }

      // Add boundaries in order: start → end → next start → next end, etc.
      // For navigation, we want to go to the actual last frame of each segment
      segments.forEach((segment, index) => {
        boundaries.push(segment.start);
        // For segment end navigation, use the last frame of the segment
        // Last segment ends at totalDuration - 1, others end at cut frame - 1
        const isLastSegment = index === segments.length - 1;
        const segmentEnd = isLastSegment ? segment.end : segment.end - 1;
        boundaries.push(segmentEnd);
      });
    } else if (overlays && overlays.length > 0) {
      // Use overlay boundaries as fallback
      const segments = overlays
        .map((overlay) => ({
          start: overlay.from,
          end: overlay.from + overlay.durationInFrames - 1,
        }))
        .sort((a, b) => a.start - b.start);

      // Add boundaries in order: start → end → next start → next end, etc.
      segments.forEach((segment) => {
        boundaries.push(segment.start);
        boundaries.push(segment.end);
      });
    } else {
      // Fallback: just start and end - 1
      boundaries.push(0);
      boundaries.push(totalDuration - 1);
    }

    // Remove duplicates while preserving order
    const uniqueBoundaries: number[] = [];
    boundaries.forEach((boundary) => {
      if (!uniqueBoundaries.includes(boundary)) {
        uniqueBoundaries.push(boundary);
      }
    });

    return uniqueBoundaries;
  }, [storeCutTimes, overlays, totalDuration, cutTimeToFrame]);

  // Move to next boundary in sequence (start → end → next start → next end, etc.)
  const moveToNextBoundary = useCallback(() => {
    if (!setCurrentFrame) return;

    const boundaries = getAllSegmentBoundaries();
    if (boundaries.length === 0) return;

    // Find current position in boundaries
    let currentIndex = -1;

    // Look for exact match first
    for (let i = 0; i < boundaries.length; i++) {
      if (Math.abs(boundaries[i] - currentFrame) < 0.01) {
        // Very precise tolerance - only for truly exact matches
        currentIndex = i;
        break;
      }
    }

    if (currentIndex !== -1) {
      // We're at a boundary, move to the next one
      currentIndex = (currentIndex + 1) % boundaries.length;
    } else {
      // We're not at a boundary, find the next boundary
      currentIndex = boundaries.findIndex((b) => b > currentFrame);
      if (currentIndex === -1) {
        // We're past all boundaries, wrap to the first one
        currentIndex = 0;
      }
    }

    const newFrame = boundaries[currentIndex];
    setCurrentFrame(newFrame);
    // Update display frame immediately for accurate visual feedback
    setDisplayFrame(newFrame);
  }, [currentFrame, setCurrentFrame, getAllSegmentBoundaries]);

  // Move to previous boundary in reverse sequence (end → start → previous end → previous start, etc.)
  const moveToPreviousBoundary = useCallback(() => {
    if (!setCurrentFrame) return;

    const boundaries = getAllSegmentBoundaries();
    if (boundaries.length === 0) return;

    // Find current position in boundaries
    let currentIndex = -1;

    // Look for exact match first
    for (let i = 0; i < boundaries.length; i++) {
      if (Math.abs(boundaries[i] - currentFrame) < 0.01) {
        // Very precise tolerance - only for truly exact matches
        currentIndex = i;
        break;
      }
    }

    if (currentIndex !== -1) {
      // We're at a boundary, move to the previous one
      currentIndex = (currentIndex - 1 + boundaries.length) % boundaries.length;
    } else {
      // We're not at a boundary, find the previous boundary
      for (let i = boundaries.length - 1; i >= 0; i--) {
        if (boundaries[i] < currentFrame) {
          currentIndex = i;
          break;
        }
      }
      if (currentIndex === -1) {
        // We're before all boundaries, wrap to the last one
        currentIndex = boundaries.length - 1;
      }
    }

    const newFrame = boundaries[currentIndex];
    setCurrentFrame(newFrame);
    // Update display frame immediately for accurate visual feedback
    setDisplayFrame(newFrame);
  }, [currentFrame, setCurrentFrame, getAllSegmentBoundaries]);

  const parseTimeData = useCallback(
    (input: string, fps: number = effectiveFPS) => {
      const [hms, frameStr] = input.split(".");
      const [hh, mm, ss] = hms.split(":").map(Number);
      const frames = Number(frameStr);
      return {
        input,
        hours: hh,
        minutes: mm,
        seconds: ss,
        frames,
        fps,
      };
    },
    [effectiveFPS]
  );
  const displayTime = useCallback(
    (frames: number) => {
      if (externalFormatTime) {
        return externalFormatTime(frames);
      }

      return formatTime(frames, effectiveFPS);
    },
    [externalFormatTime, effectiveFPS]
  );
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Check for Ctrl/Cmd key combinations
      const isCmdOrCtrl = event.metaKey || event.ctrlKey;
      if (isCmdOrCtrl) {
        switch (event.key) {
          case "+":
          case "=": // Also handle the = key (same key as + without shift)
            event.preventDefault();
            moveToNextBoundary();
            break;
          case "-":
          case "_": // Also handle the _ key (same key as - with shift)
            event.preventDefault();
            moveToPreviousBoundary();
            break;
        }
      } else {
        // Handle arrow keys for frame navigation and other shortcuts
        switch (event.key) {
          case "ArrowRight":
            event.preventDefault();
            goToNextFrame();
            break;
          case "ArrowLeft":
            event.preventDefault();
            goToPreviousFrame();
            break;
          case "t":
          case "T":
            event.preventDefault();
            handleZoomIn();
            break;
          case "r":
          case "R":
            event.preventDefault();
            handleZoomOut();
            break;
        }
      }
    };
    // Add event listener
    window.addEventListener("keydown", handleKeyPress);
    // Cleanup function
    return () => {
      window.removeEventListener("keydown", handleKeyPress);
    };
  }, [
    goToNextFrame,
    goToPreviousFrame,
    handleZoomIn,
    handleZoomOut,
    moveToNextBoundary,
    moveToPreviousBoundary,
  ]);
  // Listen for undo/redo events to clear cut times since they may no longer be valid
  useEffect(() => {
    const handleUndo = () => {
      // Clear all cut times when undo is triggered since overlays change
      // User can recreate cuts if needed for the new state
      clearCutTimes();
    };
    const handleRedo = () => {
      // Clear all cut times when redo is triggered since overlays change
      // User can recreate cuts if needed for the new state
      clearCutTimes();
    };
    // Add event listeners for undo/redo events
    window.addEventListener("editor-undo", handleUndo);
    window.addEventListener("editor-redo", handleRedo);
    // Cleanup function
    return () => {
      window.removeEventListener("editor-undo", handleUndo);
      window.removeEventListener("editor-redo", handleRedo);
    };
  }, [clearCutTimes]);
  const handleCreateSegment = (timeInSeconds: number) => {
    if (onSplitAtTime) {
      // Convert time to frame position using the actual effective FPS
      const frameAtTime = Math.round(timeInSeconds * effectiveFPS);
      onSplitAtTime(frameAtTime);
    }
  };
  const [cutTimes, setCutTimes] = useState<
    {
      input: string;
      hours: number;
      minutes: number;
      seconds: number;
      frames: number;
      fps: number;
    }[]
  >([]);
  useEffect(() => {
    if (cutTimes.length > 0) {
      setTimeFrameSegment(cutTimes);
    }
  }, [cutTimes, setTimeFrameSegment]);
  return (
    <div className="flex justify-between items-center border-b border-gray-200 dark:border-gray-800 bg-white/95 dark:bg-gray-900/30 px-3 py-3 backdrop-blur-sm border-l">
      <div className="flex items-center gap-1 flex-1 justify-start">
        <TooltipProvider delayDuration={50}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={undo}
                disabled={!canUndo}
                size="icon"
                variant="ghost"
                className="h-7 w-7 text-gray-700 dark:text-zinc-200 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/80 dark:hover:bg-gray-800/80"
              >
                <Undo2 className="h-3.5 w-3.5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent
              side="top"
              sideOffset={5}
              className="bg-white dark:bg-gray-900 text-xs px-2 py-1 rounded-md z-[9999] border border-gray-200 dark:border-gray-700"
              align="start"
            >
              <div className="flex items-center gap-1">
                <span className="text-gray-700 dark:text-zinc-200">Undo</span>
                <kbd className="px-1 py-0.5 text-[10px] font-mono bg-gray-800 dark:bg-gray-800 text-white rounded-md border border-gray-700">
                  ⌘Z
                </kbd>
              </div>
            </TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={redo}
                disabled={!canRedo}
                size="icon"
                variant="ghost"
                className="h-7 w-7 text-gray-700 dark:text-zinc-200 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/80 dark:hover:bg-gray-800/80"
              >
                <Redo2 className="h-3.5 w-3.5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent
              side="top"
              sideOffset={5}
              className="bg-white dark:bg-gray-900 text-xs px-2 py-1 rounded-md z-[9999] border border-gray-200 dark:border-gray-700"
              align="start"
            >
              <div className="flex items-center gap-1">
                <span className="text-gray-700 dark:text-zinc-200">Redo</span>
                <kbd className="px-1 py-0.5 text-[10px] font-mono bg-gray-800 dark:bg-gray-800 text-white rounded-md border border-gray-700">
                  ⌘Y
                </kbd>
              </div>
            </TooltipContent>
          </Tooltip>
          {/* Split Button */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={() => {
                  const timeData = displayTime(currentFrame);
                  const parsedTime = parseTimeData(timeData);
                  setCutTimes((prev) => [...prev, parsedTime]);
                  addCutTime(parsedTime);
                  if (onSplitAtTime) {
                    onSplitAtTime(currentFrame, timeData); // Pass both frame and time data
                  }
                }}
                size="default"
                variant="ghost"
                className="text-gray-700 dark:text-zinc-200 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/80 dark:hover:bg-gray-800/80 px-4"
              >
                <Scissors className="h-3.5 w-3.5 shrink-0" />
                <span className="text-xs">Cut</span>
              </Button>
            </TooltipTrigger>
          </Tooltip>
        </TooltipProvider>
      </div>
      <div className="flex items-center justify-center gap-2 flex-grow">
        {setCurrentFrame && (
          <div className="flex items-center gap-1">
            <TooltipProvider delayDuration={50}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={goToPreviousFrame}
                    size="icon"
                    variant="ghost"
                    className="h-6 w-6 text-gray-700 dark:text-zinc-200 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/80 dark:hover:bg-gray-800/80"
                  >
                    <ChevronLeft className="h-3.5 w-3.5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent
                  side="top"
                  sideOffset={5}
                  className="bg-white dark:bg-gray-900 text-xs px-2 py-1 rounded-md z-[9999] border border-gray-200 dark:border-gray-700"
                >
                  <span className="text-gray-700 dark:text-zinc-200">
                    Previous Frame
                  </span>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <TooltipProvider delayDuration={50}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={goToNextFrame}
                    size="icon"
                    variant="ghost"
                    className="h-6 w-6 text-gray-700 dark:text-zinc-200 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/80 dark:hover:bg-gray-800/80"
                  >
                    <ChevronRight className="h-3.5 w-3.5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent
                  side="top"
                  sideOffset={5}
                  className="bg-white dark:bg-gray-900 text-xs px-2 py-1 rounded-md z-[9999] border border-gray-200 dark:border-gray-700"
                >
                  <span className="text-gray-700 dark:text-zinc-200">
                    Next Frame
                  </span>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="hidden sm:flex border h-7 p-3 text-xs text-gray-500 dark:text-zinc-400 hover:text-gray-700 dark:hover:text-zinc-300 hover:bg-transparent"
            >
              {playbackRate}x
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="min-w-[100px] bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700"
            align="center"
          >
            {[0.25, 0.5, 1, 1.5, 2].map((speed) => (
              <DropdownMenuItem
                key={speed}
                onClick={() => setPlaybackRate(speed)}
                className={`text-xs py-1.5 ${
                  playbackRate === speed
                    ? "text-blue-600 dark:text-blue-400 font-medium"
                    : "text-gray-600 dark:text-zinc-400"
                }`}
              >
                {speed}x
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
        <TooltipProvider delayDuration={50}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={handlePlayPause}
                size="sm"
                variant="default"
                className="h-7 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                {isPlaying ? (
                  <Pause className="h-3 w-3 text-gray-700 dark:text-white" />
                ) : (
                  <Play className="h-3 w-3 text-gray-700 dark:text-white" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent
              side="top"
              sideOffset={5}
              className="bg-white dark:bg-gray-900 text-xs px-2 py-1 rounded-md z-[9999] border border-gray-200 dark:border-gray-700"
              align="center"
            >
              <div className="flex items-center gap-1">
                <span className="text-gray-700 dark:text-zinc-200">
                  {isPlaying ? "Pause" : "Play"}
                </span>
                <kbd className="px-1 py-0.5 text-[10px] font-mono bg-gray-800 dark:bg-gray-800 text-white rounded-md border border-gray-700">
                  ⌥ Space
                </kbd>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <div className="flex items-center space-x-1">
          <span className="text-xs font-medium text-gray-900 dark:text-white tabular-nums font-mono">
            {displayTime(displayFrame)}
          </span>
          <span className="text-xs font-medium text-gray-500 dark:text-zinc-500">
            /
          </span>
          <span className="text-xs font-medium text-gray-500 dark:text-zinc-400 tabular-nums font-mono">
            {displayTime(totalDuration)}
          </span>
        </div>
        {onSplitAtTime && (
          <>
            <CreateSegmentPopup
              onCreateSegment={handleCreateSegment}
              currentFrame={currentFrame}
              fps={effectiveFPS}
              formatTime={displayTime}
            />
            <AutoCutPopup
              onCreateSegment={handleCreateSegment}
              currentFrame={currentFrame}
              fps={effectiveFPS}
              formatTime={displayTime}
            />
          </>
        )}
      </div>
      <div className="flex items-center gap-3 flex-1 justify-end">
        <div className="hidden sm:flex items-center gap-1 w-40">
          <TooltipProvider delayDuration={50}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={handleZoomOut}
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 text-gray-500 dark:text-zinc-400 hover:bg-gray-100/80 dark:hover:bg-gray-800/80"
                  disabled={zoomScale <= ZOOM_CONSTRAINTS.min}
                >
                  <ZoomOut className="h-3.5 w-3.5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent
                side="top"
                sideOffset={5}
                className="bg-white dark:bg-gray-900 text-xs px-2 py-1 rounded-md z-[9999] border border-gray-200 dark:border-gray-700"
                align="center"
              >
                <div className="flex items-center gap-1">
                  <span className="text-gray-700 dark:text-zinc-200">
                    Zoom Out
                  </span>
                  <kbd className="px-1 py-0.5 text-[10px] font-mono bg-gray-800 dark:bg-gray-800 text-white rounded-md border border-gray-700">
                    R
                  </kbd>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <Slider
            value={[zoomScale * 100]}
            onValueChange={handleSliderChange}
            min={ZOOM_CONSTRAINTS.min * 100}
            max={ZOOM_CONSTRAINTS.max * 100}
            step={ZOOM_CONSTRAINTS.step * 100}
            className="w-full"
            aria-label="Timeline Zoom"
            onKeyDown={(e) => {
              if (e.key === "ArrowLeft" || e.key === "ArrowRight") {
                e.preventDefault();
                // Don't stopPropagation - let it bubble up for frame navigation
              }
            }}
          />
          <TooltipProvider delayDuration={50}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={handleZoomIn}
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 text-gray-500 dark:text-zinc-400 hover:bg-gray-100/80 dark:hover:bg-gray-800/80"
                  disabled={zoomScale >= ZOOM_CONSTRAINTS.max}
                >
                  <ZoomIn className="h-3.5 w-3.5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent
                side="top"
                sideOffset={5}
                className="bg-white dark:bg-gray-900 text-xs px-2 py-1 rounded-md z-[9999] border border-gray-200 dark:border-gray-700"
                align="center"
              >
                <div className="flex items-center gap-1">
                  <span className="text-gray-700 dark:text-zinc-200">
                    Zoom In
                  </span>
                  <kbd className="px-1 py-0.5 text-[10px] font-mono bg-gray-800 dark:bg-gray-800 text-white rounded-md border border-gray-700">
                    T
                  </kbd>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>{" "}
        <TooltipProvider delayDuration={50}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={handleResetZoom}
                variant="ghost"
                size="icon"
                className="hidden sm:block h-7 w-7 text-gray-700 dark:text-zinc-200 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/80 dark:hover:bg-gray-800/80 transition-colors rounded-md"
              >
                <SquareSquare className="h-3.5 w-3.5 m-auto" />
              </Button>
            </TooltipTrigger>
            <TooltipContent
              side="top"
              sideOffset={5}
              className="bg-white dark:bg-gray-900 text-xs px-2 py-1 rounded-md z-[9999] border border-gray-200 dark:border-gray-700"
              align="end"
            >
              <span className="text-gray-700 dark:text-zinc-200">
                Reset Zoom
              </span>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <Separator orientation="vertical" className="h-7" />
      </div>
    </div>
  );
};
